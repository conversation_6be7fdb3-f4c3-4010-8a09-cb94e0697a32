import { useState, useCallback, useRef } from 'react';
import { AnalysisResult } from '@/types';

interface AnalyzedFile {
  filename: string;
  path: string;
  timestamp: number;
}

interface AnalysisState {
  isAnalyzing: boolean;
  progress: number;
  status: string;
  analyzedFiles: AnalyzedFile[];
  aiStatus: string;
  result: AnalysisResult | null;
  error: string | null;
}

export function useRealTimeAnalysis() {
  const [state, setState] = useState<AnalysisState>({
    isAnalyzing: false,
    progress: 0,
    status: '',
    analyzedFiles: [],
    aiStatus: '',
    result: null,
    error: null,
  });

  const eventSourceRef = useRef<EventSource | null>(null);

  const startAnalysis = useCallback(async (url: string, token?: string) => {
    // Reset state
    setState({
      isAnalyzing: true,
      progress: 0,
      status: 'Analiz başlatılıyor...',
      analyzedFiles: [],
      aiStatus: '',
      result: null,
      error: null,
    });

    try {
      // Close existing connection if any
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      // Create EventSource with POST data
      const response = await fetch('/api/analyze-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, token: token?.trim() || undefined }),
      });

      if (!response.ok) {
        throw new Error('Failed to start analysis stream');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6));
              handleStreamEvent(eventData);
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', parseError);
            }
          }
        }
      }

    } catch (error) {
      console.error('Analysis stream error:', error);
      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        error: error instanceof Error ? error.message : 'Analiz sırasında bir hata oluştu',
      }));
    }
  }, []);

  const handleStreamEvent = useCallback((event: any) => {
    switch (event.type) {
      case 'progress':
        setState(prev => ({
          ...prev,
          progress: event.data.progress || prev.progress,
        }));
        break;

      case 'status':
        setState(prev => ({
          ...prev,
          status: event.data.message,
          progress: event.data.progress || prev.progress,
        }));
        break;

      case 'file-analyzed':
        setState(prev => ({
          ...prev,
          analyzedFiles: [
            ...prev.analyzedFiles,
            {
              filename: event.data.filename,
              path: event.data.path,
              timestamp: Date.now(),
            }
          ],
          progress: event.data.progress || prev.progress,
        }));
        break;

      case 'ai-analysis':
        setState(prev => ({
          ...prev,
          aiStatus: event.data.message,
        }));
        break;

      case 'complete':
        setState(prev => ({
          ...prev,
          isAnalyzing: false,
          progress: 100,
          status: 'Analiz tamamlandı!',
          result: event.data.result,
          aiStatus: '',
        }));
        break;

      case 'error':
        setState(prev => ({
          ...prev,
          isAnalyzing: false,
          error: event.data.message,
          aiStatus: '',
        }));
        break;

      default:
        console.warn('Unknown event type:', event.type);
    }
  }, []);

  const stopAnalysis = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    setState(prev => ({
      ...prev,
      isAnalyzing: false,
      status: 'Analiz durduruldu',
    }));
  }, []);

  const resetAnalysis = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    setState({
      isAnalyzing: false,
      progress: 0,
      status: '',
      analyzedFiles: [],
      aiStatus: '',
      result: null,
      error: null,
    });
  }, []);

  return {
    ...state,
    startAnalysis,
    stopAnalysis,
    resetAnalysis,
  };
}
