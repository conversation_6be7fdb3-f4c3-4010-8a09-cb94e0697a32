'use client';

import { useState } from 'react';
import { RealTimeAnalysisView } from '@/components/RealTimeAnalysisView';
import { AIProviderConfig } from '@/components/AIProviderConfig';
import { Settings } from 'lucide-react';

export default function Home() {
  const [showAIConfig, setShowAIConfig] = useState(false);

  const handleAIConfigSave = (config: {
    provider: string;
    apiKey: string;
    baseURL: string;
    model: string;
    maxTokens: number;
    temperature: number;
  }) => {
    // Save AI configuration to localStorage or send to backend
    localStorage.setItem('aiProviderConfig', JSON.stringify(config));
    console.log('AI Configuration saved:', config);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                BaseGraph
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                GitHub kod tabanı görselleştirme ve analiz aracı
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowAIConfig(true)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-md hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span>AI Settings</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <RealTimeAnalysisView />
      </main>

      {/* AI Provider Configuration Modal */}
      <AIProviderConfig
        isOpen={showAIConfig}
        onClose={() => setShowAIConfig(false)}
        onSave={handleAIConfigSave}
      />
    </div>
  );
}
