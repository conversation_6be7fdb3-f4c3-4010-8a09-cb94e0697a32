import { NextRequest } from 'next/server';
import { GitHubService } from '@/lib/github';
import { CodeAnalyzer } from '@/lib/codeAnalyzer';
import { AnalysisResult, FileNode, CodeGraph } from '@/types';

interface StreamEvent {
  type: 'progress' | 'status' | 'file-analyzed' | 'ai-analysis' | 'complete' | 'error';
  data: any;
}

function sendEvent(controller: ReadableStreamDefaultController, event: StreamEvent) {
  const message = `data: ${JSON.stringify(event)}\n\n`;
  controller.enqueue(new TextEncoder().encode(message));
}

export async function POST(request: NextRequest) {
  const { url, token } = await request.json();

  if (!url) {
    return new Response('Repository URL is required', { status: 400 });
  }

  const headers = {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  const stream = new ReadableStream({
    start(controller) {
      analyzeWithProgress(controller, url, token);
    }
  });

  return new Response(stream, { headers });
}

async function analyzeWithProgress(
  controller: ReadableStreamDefaultController,
  url: string,
  token?: string
) {
  try {
    // Environment variable'dan token'ı al
    const githubToken = token || process.env.GITHUB_TOKEN;
    const githubService = new GitHubService(githubToken);

    sendEvent(controller, {
      type: 'status',
      data: { message: 'GitHub URL analiz ediliyor...', progress: 5 }
    });

    const repoInfo = githubService.parseRepoUrl(url);
    if (!repoInfo) {
      sendEvent(controller, {
        type: 'error',
        data: { message: 'Geçersiz GitHub URL formatı' }
      });
      controller.close();
      return;
    }

    const { owner, repo } = repoInfo;

    sendEvent(controller, {
      type: 'status',
      data: { message: `Repository bilgileri alınıyor: ${owner}/${repo}`, progress: 10 }
    });

    // Repository bilgilerini al
    const repository = await githubService.getRepository(owner, repo);
    const languages = await githubService.getRepositoryLanguages(owner, repo);

    sendEvent(controller, {
      type: 'status',
      data: { message: 'Repository içeriği taranıyor...', progress: 20 }
    });

    // Repository içeriğini analiz et (progress ile)
    const nodes = await analyzeRepositoryContentsWithProgress(
      controller,
      githubService,
      owner,
      repo
    );

    sendEvent(controller, {
      type: 'status',
      data: { message: 'Bağımlılık grafı oluşturuluyor...', progress: 90 }
    });

    const edges = CodeAnalyzer.createDependencyEdges(nodes);
    const graph: CodeGraph = { nodes, edges };

    const result: AnalysisResult = {
      repo: repository,
      graph,
      languages,
      stats: {
        totalFiles: nodes.filter(n => n.type === 'file').length,
        totalDirectories: nodes.filter(n => n.type === 'directory').length,
        totalDependencies: edges.filter(e => e.type === 'import' || e.type === 'require' || e.type === 'include').length,
        totalFunctions: nodes.reduce((sum, n) => sum + (n.functions?.length || 0), 0),
        totalFunctionCalls: edges.filter(e => e.type === 'function_call').length,
      },
    };

    sendEvent(controller, {
      type: 'complete',
      data: { result, progress: 100 }
    });

  } catch (error) {
    console.error('Stream Analysis Error:', error);
    sendEvent(controller, {
      type: 'error',
      data: { message: error instanceof Error ? error.message : 'Analiz sırasında bir hata oluştu' }
    });
  } finally {
    controller.close();
  }
}

async function analyzeRepositoryContentsWithProgress(
  controller: ReadableStreamDefaultController,
  githubService: GitHubService,
  owner: string,
  repo: string,
  path: string = '',
  depth: number = 0
): Promise<FileNode[]> {
  const nodes: FileNode[] = [];
  const maxDepth = 2; // Maksimum derinlik sınırı
  const maxFiles = 30; // Maksimum dosya sayısı sınırı

  if (depth >= maxDepth) {
    return nodes;
  }

  try {
    const contents = await githubService.getRepositoryContents(owner, repo, path);
    
    // İçerikleri sırala: önce klasörler, sonra dosyalar
    const sortedContents = contents.sort((a, b) => {
      if (a.type === 'dir' && b.type === 'file') return -1;
      if (a.type === 'file' && b.type === 'dir') return 1;
      return a.name.localeCompare(b.name);
    });

    let fileCount = 0;
    const totalItems = Math.min(sortedContents.length, maxFiles);

    for (let i = 0; i < sortedContents.length && fileCount < maxFiles; i++) {
      const item = sortedContents[i];
      const progress = 20 + (60 * (i + 1) / totalItems); // 20-80 arası progress

      const nodeId = `${path}/${item.name}`.replace(/^\//, '') || item.name;
      const node: FileNode = {
        id: nodeId,
        name: item.name,
        path: item.path,
        type: item.type === 'dir' ? 'directory' : 'file',
        size: item.size,
      };

      if (item.type === 'file') {
        fileCount++;
        
        sendEvent(controller, {
          type: 'file-analyzed',
          data: { filename: item.name, path: item.path, progress }
        });

        const language = CodeAnalyzer.getLanguageFromExtension(item.name);
        if (language) {
          node.language = language;
          
          // Sadece küçük dosyaları analiz et (30KB'den küçük)
          if (item.size && item.size < 30000) {
            try {
              sendEvent(controller, {
                type: 'ai-analysis',
                data: { message: `${item.name} dosyası AI ile analiz ediliyor...` }
              });

              const content = await githubService.getFileContent(owner, repo, item.path);
              node.content = content;
              node.dependencies = CodeAnalyzer.extractDependencies(content, language);

              // Extract functions using AI or fallback to regex
              try {
                node.functions = await CodeAnalyzer.extractFunctions(content, language, item.name);
              } catch (functionError) {
                console.warn(`Could not extract functions for ${item.path}:`, functionError);
                node.functions = [];
              }
            } catch (contentError) {
              console.warn(`Could not fetch content for ${item.path}:`, contentError);
            }
          }
        }
      } else if (item.type === 'dir') {
        // Belirli klasörleri atla
        const skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', 'coverage', '__pycache__'];
        if (!skipDirs.includes(item.name)) {
          const children = await analyzeRepositoryContentsWithProgress(
            controller,
            githubService,
            owner,
            repo,
            item.path,
            depth + 1
          );
          node.children = children;
          nodes.push(...children);
        }
      }

      nodes.push(node);
    }
  } catch (error) {
    console.warn(`Could not analyze path ${path}:`, error);
  }

  return nodes;
}
