import { Octokit } from '@octokit/rest';
import { GitHubRepo } from '@/types';

export class GitHubService {
  private octokit: Octokit;
  private rateLimitRemaining: number = 60;
  private rateLimitReset: number = 0;

  constructor(token?: string) {
    // Token durumunu logla (sadece development'ta)
    if (process.env.NODE_ENV === 'development') {
      if (token) {
        console.log('GitHub token provided, using authenticated requests');
      } else {
        console.log('No GitHub token provided, using unauthenticated requests (rate limit: 60/hour)');
      }
    }

    this.octokit = new Octokit({
      auth: token,
      request: {
        retries: 3,
        retryAfter: 2,
      },
    });
  }

  private async checkRateLimit(): Promise<void> {
    try {
      const { data } = await this.octokit.rest.rateLimit.get();
      this.rateLimitRemaining = data.rate.remaining;
      this.rateLimitReset = data.rate.reset;

      // Development'ta rate limit durumunu logla
      if (process.env.NODE_ENV === 'development') {
        console.log(`GitHubService - Rate limit status: ${this.rateLimitRemaining} requests remaining`);
      }

      // Sadece rate limit tamamen bittiğinde hata ver
      if (this.rateLimitRemaining <= 0) {
        const resetTime = new Date(this.rateLimitReset * 1000);
        const waitTime = resetTime.getTime() - Date.now();

        if (waitTime > 0) {
          console.warn(`GitHub API rate limit exceeded (${this.rateLimitRemaining} remaining). Reset at ${resetTime.toISOString()}`);
          throw new Error(`GitHub API rate limit exceeded. Please wait until ${resetTime.toLocaleString()} or use a GitHub token for higher limits.`);
        }
      } else if (this.rateLimitRemaining < 10) {
        // Sadece uyarı ver, hata verme
        const resetTime = new Date(this.rateLimitReset * 1000);
        console.warn(`GitHub API rate limit low (${this.rateLimitRemaining} remaining). Reset at ${resetTime.toISOString()}`);
      }
    } catch (error) {
      // Rate limit kontrolü başarısız olursa, devam et ama hata logla
      console.warn('Failed to check GitHub rate limit:', error);
      // Eğer bu bir rate limit hatası ise, yeniden fırlat
      if (error instanceof Error && error.message.includes('rate limit')) {
        throw error;
      }
    }
  }

  private handleGitHubError(error: unknown, context: string): never {
    // Type guard for GitHub API errors
    const isGitHubError = (err: unknown): err is { status: number; message?: string; response?: { headers?: Record<string, string> } } => {
      return typeof err === 'object' && err !== null && 'status' in err;
    };

    if (isGitHubError(error)) {
      if (error.status === 403 && error.message?.includes('rate limit')) {
        const resetTime = error.response?.headers?.['x-ratelimit-reset'];
        const resetDate = resetTime ? new Date(parseInt(resetTime) * 1000) : new Date(Date.now() + 3600000);
        const remaining = error.response?.headers?.['x-ratelimit-remaining'] || '0';

        throw new Error(
          `GitHub API rate limit exceeded (${remaining} requests remaining). ` +
          `Reset time: ${resetDate.toLocaleString()}. ` +
          `Current token provides ${process.env.GITHUB_TOKEN ? 'authenticated' : 'unauthenticated'} access. ` +
          `For higher limits (5000/hour), ensure a valid GitHub token is configured.`
        );
      }

      if (error.status === 404) {
        throw new Error(`Repository not found or is private. Please check the URL and ensure the repository is public.`);
      }

      if (error.status === 401) {
        throw new Error(`GitHub authentication failed. Please check your token.`);
      }
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`${context}: ${errorMessage}`);
  }

  async getRepository(owner: string, repo: string): Promise<GitHubRepo> {
    try {
      await this.checkRateLimit();

      const { data } = await this.octokit.rest.repos.get({
        owner,
        repo,
      });

      // Development'ta başarılı fetch'i logla
      if (process.env.NODE_ENV === 'development') {
        console.log(`GitHubService - Repository ${owner}/${repo} fetched successfully`);
      }

      return data as GitHubRepo;
    } catch (error) {
      console.error(`GitHubService - Error fetching repository ${owner}/${repo}:`, error);
      this.handleGitHubError(error, 'Failed to fetch repository');
    }
  }

  async getRepositoryContents(
    owner: string,
    repo: string,
    path: string = ''
  ): Promise<Array<{
    name: string;
    path: string;
    type: string;
    size?: number;
    content?: string;
  }>> {
    try {
      const { data } = await this.octokit.rest.repos.getContent({
        owner,
        repo,
        path,
      });
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      this.handleGitHubError(error, `Failed to fetch repository contents for path: ${path}`);
    }
  }

  async getFileContent(
    owner: string,
    repo: string,
    path: string
  ): Promise<string> {
    try {
      const { data } = await this.octokit.rest.repos.getContent({
        owner,
        repo,
        path,
      });

      if ('content' in data && data.content) {
        return Buffer.from(data.content, 'base64').toString('utf-8');
      }
      throw new Error('File content not found');
    } catch (error) {
      this.handleGitHubError(error, `Failed to fetch file content for: ${path}`);
    }
  }

  async getRepositoryLanguages(
    owner: string,
    repo: string
  ): Promise<Record<string, number>> {
    try {
      const { data } = await this.octokit.rest.repos.listLanguages({
        owner,
        repo,
      });
      return data;
    } catch (error) {
      this.handleGitHubError(error, 'Failed to fetch repository languages');
    }
  }

  parseRepoUrl(url: string): { owner: string; repo: string } | null {
    console.log('GitHubService - Parsing URL:', url);
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (match) {
      const result = {
        owner: match[1],
        repo: match[2].replace(/\.git$/, ''),
      };
      console.log('GitHubService - Parsed successfully:', result);
      return result;
    }
    console.log('GitHubService - URL parsing failed');
    return null;
  }
}
