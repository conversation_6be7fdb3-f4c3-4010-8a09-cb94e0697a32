import { NextRequest, NextResponse } from 'next/server';
import { GitHubService } from '@/lib/github';
import { CodeAnalyzer } from '@/lib/codeAnalyzer';
import { AnalysisResult, FileNode, CodeGraph } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { url, token } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'Repository URL is required' },
        { status: 400 }
      );
    }

    // Environment variable'dan token'ı al, eğer frontend'den gönderilmemişse
    const githubToken = token || process.env.GITHUB_TOKEN;
    const githubService = new GitHubService(githubToken);
    const repoInfo = githubService.parseRepoUrl(url);

    if (!repoInfo) {
      return NextResponse.json(
        { error: 'Invalid GitHub URL format' },
        { status: 400 }
      );
    }

    const { owner, repo } = repoInfo;

    try {
      // Repository bilgilerini al
      const repository = await githubService.getRepository(owner, repo);
      const languages = await githubService.getRepositoryLanguages(owner, repo);

      // Repository içeriğini analiz et
      const nodes = await analyzeRepositoryContents(githubService, owner, repo);
      const edges = CodeAnalyzer.createDependencyEdges(nodes);

      const graph: CodeGraph = { nodes, edges };

      const result: AnalysisResult = {
        repo: repository,
        graph,
        languages,
        stats: {
          totalFiles: nodes.filter(n => n.type === 'file').length,
          totalDirectories: nodes.filter(n => n.type === 'directory').length,
          totalDependencies: edges.filter(e => e.type === 'import' || e.type === 'require' || e.type === 'include').length,
          totalFunctions: nodes.reduce((sum, n) => sum + (n.functions?.length || 0), 0),
          totalFunctionCalls: edges.filter(e => e.type === 'function_call').length,
        },
      };

      return NextResponse.json(result);
    } catch (error) {
      console.error('GitHub API Error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch repository data. Please check if the repository exists and is public.' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Analysis Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function analyzeRepositoryContents(
  githubService: GitHubService,
  owner: string,
  repo: string,
  path: string = '',
  depth: number = 0
): Promise<FileNode[]> {
  const nodes: FileNode[] = [];
  const maxDepth = 3; // Maksimum derinlik sınırı
  const maxFiles = 100; // Maksimum dosya sayısı sınırı

  if (depth >= maxDepth) {
    return nodes;
  }

  try {
    const contents = await githubService.getRepositoryContents(owner, repo, path);
    
    // İçerikleri sırala: önce klasörler, sonra dosyalar
    const sortedContents = contents.sort((a, b) => {
      if (a.type === 'dir' && b.type === 'file') return -1;
      if (a.type === 'file' && b.type === 'dir') return 1;
      return a.name.localeCompare(b.name);
    });

    let fileCount = 0;

    for (const item of sortedContents) {
      if (fileCount >= maxFiles) break;

      const nodeId = `${path}/${item.name}`.replace(/^\//, '') || item.name;
      const node: FileNode = {
        id: nodeId,
        name: item.name,
        path: item.path,
        type: item.type === 'dir' ? 'directory' : 'file',
        size: item.size,
      };

      if (item.type === 'file') {
        fileCount++;
        const language = CodeAnalyzer.getLanguageFromExtension(item.name);
        if (language) {
          node.language = language;
          
          // Sadece küçük dosyaları analiz et (50KB'den küçük)
          if (item.size && item.size < 50000) {
            try {
              const content = await githubService.getFileContent(owner, repo, item.path);
              node.content = content;
              node.dependencies = CodeAnalyzer.extractDependencies(content, language);

              // Extract functions using AI or fallback to regex
              try {
                node.functions = await CodeAnalyzer.extractFunctions(content, language, item.name);
              } catch (functionError) {
                console.warn(`Could not extract functions for ${item.path}:`, functionError);
                node.functions = [];
              }
            } catch (contentError) {
              console.warn(`Could not fetch content for ${item.path}:`, contentError);
            }
          }
        }
      } else if (item.type === 'dir') {
        // Belirli klasörleri atla
        const skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', 'coverage', '__pycache__'];
        if (!skipDirs.includes(item.name)) {
          const children = await analyzeRepositoryContents(
            githubService,
            owner,
            repo,
            item.path,
            depth + 1
          );
          node.children = children;
          nodes.push(...children);
        }
      }

      nodes.push(node);
    }
  } catch (error) {
    console.warn(`Could not analyze path ${path}:`, error);
  }

  return nodes;
}
