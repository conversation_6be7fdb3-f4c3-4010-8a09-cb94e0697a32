'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRealTimeAnalysis } from '@/hooks/useRealTimeAnalysis';
import { CodeGraphViewer } from './CodeGraphViewer';
import { 
  Github, 
  AlertCircle, 
  Key, 
  Info, 
  CheckCircle, 
  Loader2, 
  StopCircle,
  FileText,
  Brain,
  Activity
} from 'lucide-react';

export function RealTimeAnalysisView() {
  const [url, setUrl] = useState('');
  const [token, setToken] = useState('');
  const [showTokenInput, setShowTokenInput] = useState(false);
  
  const {
    isAnalyzing,
    progress,
    status,
    analyzedFiles,
    aiStatus,
    result,
    error,
    startAnalysis,
    stopAnalysis,
    resetAnalysis,
  } = useRealTimeAnalysis();

  const handleStartAnalysis = async () => {
    if (!url.trim()) {
      return;
    }
    await startAnalysis(url, token);
  };

  const handleReset = () => {
    resetAnalysis();
    setUrl('');
    setToken('');
  };

  // Eğer analiz tamamlandıysa sonuçları göster
  if (result) {
    return (
      <CodeGraphViewer
        analysisResult={result}
        onReset={handleReset}
      />
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* URL Input Section */}
      {!isAnalyzing && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            GitHub Repository Analizi
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Bir GitHub repository URL'si girin ve gerçek zamanlı analiz sürecini izleyin
          </p>
          
          <div className="space-y-4">
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  type="url"
                  placeholder="https://github.com/username/repository"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button
                onClick={handleStartAnalysis}
                disabled={!url.trim()}
                className="flex items-center space-x-2"
              >
                <Github className="w-4 h-4" />
                <span>Analiz Et</span>
              </Button>
            </div>

            {/* GitHub Token Input */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={() => setShowTokenInput(!showTokenInput)}
                  className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  <Key className="w-4 h-4" />
                  <span>GitHub Token (İsteğe bağlı)</span>
                </button>
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Info className="w-3 h-3" />
                  <span>Rate limit için</span>
                </div>
              </div>

              {showTokenInput && (
                <div className="space-y-2">
                  <Input
                    type="password"
                    placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                    value={token}
                    onChange={(e) => setToken(e.target.value)}
                    className="w-full"
                  />
                  <div className="text-xs text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                    <p className="font-medium text-blue-800 dark:text-blue-300 mb-1">GitHub Token Faydaları:</p>
                    <ul className="list-disc list-inside space-y-0.5 text-blue-700 dark:text-blue-400">
                      <li>Daha yüksek API rate limit (5000/saat)</li>
                      <li>Private repository'lere erişim</li>
                      <li>Daha hızlı analiz</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Real-time Analysis View */}
      {isAnalyzing && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Repository Analiz Ediliyor
            </h2>
            <Button
              onClick={stopAnalysis}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <StopCircle className="w-4 h-4" />
              <span>Durdur</span>
            </Button>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                İlerleme
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round(progress)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Current Status */}
          <div className="mb-6">
            <div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300">
              <Activity className="w-5 h-5 text-blue-600" />
              <span className="font-medium">Durum:</span>
              <span>{status}</span>
            </div>
          </div>

          {/* AI Analysis Status */}
          {aiStatus && (
            <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-800 dark:text-blue-300">AI Analizi:</span>
              </div>
              <div className="flex items-center space-x-2 mt-2">
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                <span className="text-blue-700 dark:text-blue-400">{aiStatus}</span>
              </div>
            </div>
          )}

          {/* Analyzed Files */}
          {analyzedFiles.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <FileText className="w-5 h-5 text-green-600" />
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Analiz Edilen Dosyalar ({analyzedFiles.length})
                </h3>
              </div>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {analyzedFiles.slice(-10).map((file, index) => (
                  <div key={`${file.path}-${file.timestamp}`} className="flex items-center space-x-2 py-1">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {file.filename}
                    </span>
                    <span className="text-xs text-gray-400 dark:text-gray-500 flex-shrink-0">
                      {file.path}
                    </span>
                  </div>
                ))}
                {analyzedFiles.length > 10 && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-2">
                    ... ve {analyzedFiles.length - 10} dosya daha
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start space-x-2 text-red-600 dark:text-red-400">
            <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div className="space-y-1">
              <span className="text-sm font-medium">Hata oluştu:</span>
              <p className="text-sm">{error}</p>
              <Button
                onClick={handleReset}
                variant="outline"
                size="sm"
                className="mt-2"
              >
                Yeniden Dene
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
