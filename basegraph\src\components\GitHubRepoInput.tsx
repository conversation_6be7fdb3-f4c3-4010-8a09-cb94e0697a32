'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AnalysisResult } from '@/types';
import { Github, AlertCircle, Key, Info } from 'lucide-react';

interface GitHubRepoInputProps {
  onAnalysisStart: () => void;
  onAnalysisComplete: (result: AnalysisResult) => void;
}

export function GitHubRepoInput({ onAnalysisStart, onAnalysisComplete }: GitHubRepoInputProps) {
  const [url, setUrl] = useState('');
  const [token, setToken] = useState('');
  const [error, setError] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showTokenInput, setShowTokenInput] = useState(false);

  const analyzeRepository = async () => {
    if (!url.trim()) {
      setError('Lütfen bir GitHub repository URL&apos;si girin');
      return;
    }

    setError('');
    setIsAnalyzing(true);
    onAnalysisStart();

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, token: token.trim() || undefined }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analiz sırasında bir hata oluştu');
      }

      const result: AnalysisResult = await response.json();
      console.log('Analysis result received:', result);
      onAnalysisComplete(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analiz sırasında bir hata oluştu');
    } finally {
      setIsAnalyzing(false);
    }
  };



  return (
    <div className="space-y-4">
      <div className="flex space-x-2">
        <div className="flex-1">
          <Input
            type="url"
            placeholder="https://github.com/username/repository"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="w-full"
            disabled={isAnalyzing}
          />
        </div>
        <Button
          onClick={analyzeRepository}
          disabled={isAnalyzing || !url.trim()}
          className="flex items-center space-x-2"
        >
          <Github className="w-4 h-4" />
          <span>{isAnalyzing ? 'Analiz Ediliyor...' : 'Analiz Et'}</span>
        </Button>
      </div>

      {/* GitHub Token Input */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={() => setShowTokenInput(!showTokenInput)}
            className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <Key className="w-4 h-4" />
            <span>GitHub Token (İsteğe bağlı)</span>
          </button>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <Info className="w-3 h-3" />
            <span>Rate limit için</span>
          </div>
        </div>

        {showTokenInput && (
          <div className="space-y-2">
            <Input
              type="password"
              placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              className="w-full"
              disabled={isAnalyzing}
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
              <p className="font-medium text-blue-800 dark:text-blue-300 mb-1">GitHub Token Faydaları:</p>
              <ul className="list-disc list-inside space-y-0.5 text-blue-700 dark:text-blue-400">
                <li>Daha yüksek API rate limit (5000/saat)</li>
                <li>Private repository&apos;lere erişim</li>
                <li>Daha hızlı analiz</li>
              </ul>
              <p className="mt-1 text-xs">
                Token oluşturmak için: GitHub → Settings → Developer settings → Personal access tokens
              </p>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
          <div className="flex items-start space-x-2 text-red-600 dark:text-red-400">
            <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
            <div className="space-y-1">
              <span className="text-sm font-medium">{error}</span>
              {error.includes('rate limit') && (
                <div className="text-xs text-red-500 dark:text-red-400">
                  <p>💡 Çözüm önerileri:</p>
                  <ul className="list-disc list-inside mt-1 space-y-0.5">
                    <li>Yukarıdaki GitHub Token alanını kullanın</li>
                    <li>Birkaç dakika bekleyip tekrar deneyin</li>
                    <li>Daha küçük bir repository deneyin</li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500 dark:text-gray-400">
        <p>
          Örnek: https://github.com/facebook/react veya https://github.com/microsoft/vscode
        </p>
        <p className="mt-1">
          Not: Büyük repository&apos;ler için analiz biraz zaman alabilir.
        </p>
      </div>
    </div>
  );
}
