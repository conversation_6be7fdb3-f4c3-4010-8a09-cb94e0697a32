{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/lib/github.ts"], "sourcesContent": ["import { Octokit } from '@octokit/rest';\nimport { GitHubRepo } from '@/types';\n\nexport class GitHubService {\n  private octokit: Octokit;\n  private rateLimitRemaining: number = 60;\n  private rateLimitReset: number = 0;\n\n  constructor(token?: string) {\n    // Debug: Token varlığını kontrol et\n    if (token) {\n      console.log('GitHub token provided, using authenticated requests');\n    } else {\n      console.log('No GitHub token provided, using unauthenticated requests (rate limit: 60/hour)');\n    }\n\n    this.octokit = new Octokit({\n      auth: token,\n      request: {\n        retries: 3,\n        retryAfter: 2,\n      },\n    });\n  }\n\n  private async checkRateLimit(): Promise<void> {\n    try {\n      const { data } = await this.octokit.rest.rateLimit.get();\n      this.rateLimitRemaining = data.rate.remaining;\n      this.rateLimitReset = data.rate.reset;\n\n      // Sadece rate limit tamamen bittiğinde hata ver\n      if (this.rateLimitRemaining <= 0) {\n        const resetTime = new Date(this.rateLimitReset * 1000);\n        const waitTime = resetTime.getTime() - Date.now();\n\n        if (waitTime > 0) {\n          console.warn(`GitHub API rate limit exceeded (${this.rateLimitRemaining} remaining). Reset at ${resetTime.toISOString()}`);\n          throw new Error(`GitHub API rate limit exceeded. Please wait until ${resetTime.toLocaleString()} or use a GitHub token for higher limits.`);\n        }\n      } else if (this.rateLimitRemaining < 10) {\n        // Sadece uyarı ver, hata verme\n        const resetTime = new Date(this.rateLimitReset * 1000);\n        console.warn(`GitHub API rate limit low (${this.rateLimitRemaining} remaining). Reset at ${resetTime.toISOString()}`);\n      }\n    } catch (error) {\n      // Rate limit kontrolü başarısız olursa, devam et ama hata logla\n      console.warn('Failed to check GitHub rate limit:', error);\n      // Eğer bu bir rate limit hatası ise, yeniden fırlat\n      if (error instanceof Error && error.message.includes('rate limit')) {\n        throw error;\n      }\n    }\n  }\n\n  private handleGitHubError(error: unknown, context: string): never {\n    // Type guard for GitHub API errors\n    const isGitHubError = (err: unknown): err is { status: number; message?: string; response?: { headers?: Record<string, string> } } => {\n      return typeof err === 'object' && err !== null && 'status' in err;\n    };\n\n    if (isGitHubError(error)) {\n      if (error.status === 403 && error.message?.includes('rate limit')) {\n        const resetTime = error.response?.headers?.['x-ratelimit-reset'];\n        const resetDate = resetTime ? new Date(parseInt(resetTime) * 1000) : new Date(Date.now() + 3600000);\n        const remaining = error.response?.headers?.['x-ratelimit-remaining'] || '0';\n\n        throw new Error(\n          `GitHub API rate limit exceeded (${remaining} requests remaining). ` +\n          `Reset time: ${resetDate.toLocaleString()}. ` +\n          `Current token provides ${process.env.GITHUB_TOKEN ? 'authenticated' : 'unauthenticated'} access. ` +\n          `For higher limits (5000/hour), ensure a valid GitHub token is configured.`\n        );\n      }\n\n      if (error.status === 404) {\n        throw new Error(`Repository not found or is private. Please check the URL and ensure the repository is public.`);\n      }\n\n      if (error.status === 401) {\n        throw new Error(`GitHub authentication failed. Please check your token.`);\n      }\n    }\n\n    const errorMessage = error instanceof Error ? error.message : String(error);\n    throw new Error(`${context}: ${errorMessage}`);\n  }\n\n  async getRepository(owner: string, repo: string): Promise<GitHubRepo> {\n    try {\n      await this.checkRateLimit();\n\n      const { data } = await this.octokit.rest.repos.get({\n        owner,\n        repo,\n      });\n      return data as GitHubRepo;\n    } catch (error) {\n      this.handleGitHubError(error, 'Failed to fetch repository');\n    }\n  }\n\n  async getRepositoryContents(\n    owner: string,\n    repo: string,\n    path: string = ''\n  ): Promise<Array<{\n    name: string;\n    path: string;\n    type: string;\n    size?: number;\n    content?: string;\n  }>> {\n    try {\n      const { data } = await this.octokit.rest.repos.getContent({\n        owner,\n        repo,\n        path,\n      });\n      return Array.isArray(data) ? data : [data];\n    } catch (error) {\n      this.handleGitHubError(error, `Failed to fetch repository contents for path: ${path}`);\n    }\n  }\n\n  async getFileContent(\n    owner: string,\n    repo: string,\n    path: string\n  ): Promise<string> {\n    try {\n      const { data } = await this.octokit.rest.repos.getContent({\n        owner,\n        repo,\n        path,\n      });\n\n      if ('content' in data && data.content) {\n        return Buffer.from(data.content, 'base64').toString('utf-8');\n      }\n      throw new Error('File content not found');\n    } catch (error) {\n      this.handleGitHubError(error, `Failed to fetch file content for: ${path}`);\n    }\n  }\n\n  async getRepositoryLanguages(\n    owner: string,\n    repo: string\n  ): Promise<Record<string, number>> {\n    try {\n      const { data } = await this.octokit.rest.repos.listLanguages({\n        owner,\n        repo,\n      });\n      return data;\n    } catch (error) {\n      this.handleGitHubError(error, 'Failed to fetch repository languages');\n    }\n  }\n\n  parseRepoUrl(url: string): { owner: string; repo: string } | null {\n    const match = url.match(/github\\.com\\/([^\\/]+)\\/([^\\/]+)/);\n    if (match) {\n      return {\n        owner: match[1],\n        repo: match[2].replace(/\\.git$/, ''),\n      };\n    }\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACH,QAAiB;IACjB,qBAA6B,GAAG;IAChC,iBAAyB,EAAE;IAEnC,YAAY,KAAc,CAAE;QAC1B,oCAAoC;QACpC,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,OAAO,GAAG,IAAI,2JAAA,CAAA,UAAO,CAAC;YACzB,MAAM;YACN,SAAS;gBACP,SAAS;gBACT,YAAY;YACd;QACF;IACF;IAEA,MAAc,iBAAgC;QAC5C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG;YACtD,IAAI,CAAC,kBAAkB,GAAG,KAAK,IAAI,CAAC,SAAS;YAC7C,IAAI,CAAC,cAAc,GAAG,KAAK,IAAI,CAAC,KAAK;YAErC,gDAAgD;YAChD,IAAI,IAAI,CAAC,kBAAkB,IAAI,GAAG;gBAChC,MAAM,YAAY,IAAI,KAAK,IAAI,CAAC,cAAc,GAAG;gBACjD,MAAM,WAAW,UAAU,OAAO,KAAK,KAAK,GAAG;gBAE/C,IAAI,WAAW,GAAG;oBAChB,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,UAAU,WAAW,IAAI;oBACzH,MAAM,IAAI,MAAM,CAAC,kDAAkD,EAAE,UAAU,cAAc,GAAG,yCAAyC,CAAC;gBAC5I;YACF,OAAO,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI;gBACvC,+BAA+B;gBAC/B,MAAM,YAAY,IAAI,KAAK,IAAI,CAAC,cAAc,GAAG;gBACjD,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,UAAU,WAAW,IAAI;YACtH;QACF,EAAE,OAAO,OAAO;YACd,gEAAgE;YAChE,QAAQ,IAAI,CAAC,sCAAsC;YACnD,oDAAoD;YACpD,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,eAAe;gBAClE,MAAM;YACR;QACF;IACF;IAEQ,kBAAkB,KAAc,EAAE,OAAe,EAAS;QAChE,mCAAmC;QACnC,MAAM,gBAAgB,CAAC;YACrB,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,YAAY;QAChE;QAEA,IAAI,cAAc,QAAQ;YACxB,IAAI,MAAM,MAAM,KAAK,OAAO,MAAM,OAAO,EAAE,SAAS,eAAe;gBACjE,MAAM,YAAY,MAAM,QAAQ,EAAE,SAAS,CAAC,oBAAoB;gBAChE,MAAM,YAAY,YAAY,IAAI,KAAK,SAAS,aAAa,QAAQ,IAAI,KAAK,KAAK,GAAG,KAAK;gBAC3F,MAAM,YAAY,MAAM,QAAQ,EAAE,SAAS,CAAC,wBAAwB,IAAI;gBAExE,MAAM,IAAI,MACR,CAAC,gCAAgC,EAAE,UAAU,sBAAsB,CAAC,GACpE,CAAC,YAAY,EAAE,UAAU,cAAc,GAAG,EAAE,CAAC,GAC7C,CAAC,uBAAuB,EAAE,QAAQ,GAAG,CAAC,YAAY,GAAG,kBAAkB,kBAAkB,SAAS,CAAC,GACnG,CAAC,yEAAyE,CAAC;YAE/E;YAEA,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,MAAM,IAAI,MAAM,CAAC,6FAA6F,CAAC;YACjH;YAEA,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,MAAM,IAAI,MAAM,CAAC,sDAAsD,CAAC;YAC1E;QACF;QAEA,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,MAAM,IAAI,MAAM,GAAG,QAAQ,EAAE,EAAE,cAAc;IAC/C;IAEA,MAAM,cAAc,KAAa,EAAE,IAAY,EAAuB;QACpE,IAAI;YACF,MAAM,IAAI,CAAC,cAAc;YAEzB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBACjD;gBACA;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,iBAAiB,CAAC,OAAO;QAChC;IACF;IAEA,MAAM,sBACJ,KAAa,EACb,IAAY,EACZ,OAAe,EAAE,EAOf;QACF,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxD;gBACA;gBACA;YACF;YACA,OAAO,MAAM,OAAO,CAAC,QAAQ,OAAO;gBAAC;aAAK;QAC5C,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,8CAA8C,EAAE,MAAM;QACvF;IACF;IAEA,MAAM,eACJ,KAAa,EACb,IAAY,EACZ,IAAY,EACK;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxD;gBACA;gBACA;YACF;YAEA,IAAI,aAAa,QAAQ,KAAK,OAAO,EAAE;gBACrC,OAAO,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,UAAU,QAAQ,CAAC;YACtD;YACA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,kCAAkC,EAAE,MAAM;QAC3E;IACF;IAEA,MAAM,uBACJ,KAAa,EACb,IAAY,EACqB;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;gBAC3D;gBACA;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,iBAAiB,CAAC,OAAO;QAChC;IACF;IAEA,aAAa,GAAW,EAA0C;QAChE,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,IAAI,OAAO;YACT,OAAO;gBACL,OAAO,KAAK,CAAC,EAAE;gBACf,MAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU;YACnC;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/lib/aiCodeAnalyzer.ts"], "sourcesContent": ["import OpenAI from 'openai';\nimport { FunctionInfo, FunctionCall } from '@/types';\n\nexport interface AICodeAnalysisResult {\n  functions: FunctionInfo[];\n  imports: string[];\n  exports: string[];\n  classes?: {\n    name: string;\n    methods: string[];\n    startLine: number;\n    endLine: number;\n  }[];\n}\n\nexport interface AIConfig {\n  apiKey: string;\n  baseURL?: string;\n  model?: string;\n  maxTokens?: number;\n  temperature?: number;\n}\n\nexport class AICodeAnalyzer {\n  private openai: OpenAI;\n  private model: string;\n  private maxTokens: number;\n  private temperature: number;\n  private static instance: AICodeAnalyzer;\n\n  constructor(config?: AIConfig) {\n    const apiKey = config?.apiKey || process.env.OPENAI_API_KEY || '';\n    const baseURL = config?.baseURL || process.env.OPENAI_BASE_URL;\n\n    this.model = config?.model || process.env.OPENAI_MODEL || 'gpt-4o-mini';\n    this.maxTokens = config?.maxTokens || parseInt(process.env.OPENAI_MAX_TOKENS || '4000');\n    this.temperature = config?.temperature || parseFloat(process.env.OPENAI_TEMPERATURE || '0.1');\n\n    this.openai = new OpenAI({\n      apiKey,\n      baseURL,\n    });\n  }\n\n  static getInstance(config?: AIConfig): AICodeAnalyzer {\n    if (!AICodeAnalyzer.instance) {\n      AICodeAnalyzer.instance = new AICodeAnalyzer(config);\n    }\n    return AICodeAnalyzer.instance;\n  }\n\n  async analyzeCode(content: string, language: string, filename: string): Promise<AICodeAnalysisResult> {\n    try {\n      const prompt = this.createAnalysisPrompt(content, language, filename);\n      \n      const response = await this.openai.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert code analyzer. Analyze the provided code and return a detailed JSON response with function definitions, calls, imports, and exports.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: this.temperature,\n        max_tokens: this.maxTokens,\n      });\n\n      const result = response.choices[0]?.message?.content;\n      if (!result) {\n        throw new Error('No response from AI model');\n      }\n\n      return this.parseAIResponse(result);\n    } catch (error) {\n      console.error('AI Code Analysis Error:', error);\n      // Fallback to empty result\n      return {\n        functions: [],\n        imports: [],\n        exports: [],\n        classes: []\n      };\n    }\n  }\n\n  private createAnalysisPrompt(content: string, language: string, filename: string): string {\n    return `\nAnalyze this ${language} code file (${filename}) and extract detailed information about functions, classes, imports, and exports.\n\nCODE:\n\\`\\`\\`${language}\n${content}\n\\`\\`\\`\n\nPlease return a JSON response with the following structure:\n{\n  \"functions\": [\n    {\n      \"name\": \"functionName\",\n      \"startLine\": 10,\n      \"endLine\": 25,\n      \"parameters\": [\"param1: string\", \"param2: number\"],\n      \"returnType\": \"string\",\n      \"isExported\": true,\n      \"isAsync\": false,\n      \"calls\": [\n        {\n          \"functionName\": \"otherFunction\",\n          \"line\": 15,\n          \"isExternal\": false,\n          \"targetFile\": null\n        }\n      ]\n    }\n  ],\n  \"imports\": [\"./utils\", \"lodash\", \"react\"],\n  \"exports\": [\"mainFunction\", \"helperFunction\"],\n  \"classes\": [\n    {\n      \"name\": \"ClassName\",\n      \"methods\": [\"method1\", \"method2\"],\n      \"startLine\": 30,\n      \"endLine\": 50\n    }\n  ]\n}\n\nIMPORTANT RULES:\n1. Include ALL function definitions (regular functions, arrow functions, methods, constructors)\n2. For each function, list ALL function calls made within it\n3. Mark isExternal=true for calls to functions not defined in this file\n4. Include accurate line numbers (1-based indexing)\n5. Extract parameter types and return types when available\n6. Include both named and default exports\n7. Return valid JSON only, no additional text\n8. If you can't determine something, use null or empty array\n9. For function calls, include the exact line number where the call occurs\n10. Consider method calls on objects as function calls too (e.g., obj.method())\n\nFocus on accuracy and completeness. This analysis will be used to build a code dependency graph.\n`;\n  }\n\n  private parseAIResponse(response: string): AICodeAnalysisResult {\n    try {\n      // Clean the response - remove markdown code blocks if present\n      const cleanedResponse = response\n        .replace(/```json\\n?/g, '')\n        .replace(/```\\n?/g, '')\n        .trim();\n\n      const parsed = JSON.parse(cleanedResponse);\n      \n      // Validate and normalize the response\n      return {\n        functions: Array.isArray(parsed.functions) ? parsed.functions : [],\n        imports: Array.isArray(parsed.imports) ? parsed.imports : [],\n        exports: Array.isArray(parsed.exports) ? parsed.exports : [],\n        classes: Array.isArray(parsed.classes) ? parsed.classes : []\n      };\n    } catch (error) {\n      console.error('Failed to parse AI response:', error);\n      console.error('Raw response:', response);\n      \n      // Return empty result on parse error\n      return {\n        functions: [],\n        imports: [],\n        exports: [],\n        classes: []\n      };\n    }\n  }\n\n  // Cache mechanism for repeated analysis of same content\n  private static cache = new Map<string, AICodeAnalysisResult>();\n\n  async analyzeCodeWithCache(content: string, language: string, filename: string): Promise<AICodeAnalysisResult> {\n    const cacheKey = this.generateCacheKey(content, language);\n    \n    if (AICodeAnalyzer.cache.has(cacheKey)) {\n      return AICodeAnalyzer.cache.get(cacheKey)!;\n    }\n\n    const result = await this.analyzeCode(content, language, filename);\n    AICodeAnalyzer.cache.set(cacheKey, result);\n    \n    return result;\n  }\n\n  private generateCacheKey(content: string, language: string): string {\n    // Simple hash function for caching\n    let hash = 0;\n    const str = content + language;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return hash.toString();\n  }\n\n  // Clear cache when needed\n  static clearCache(): void {\n    AICodeAnalyzer.cache.clear();\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAuBO,MAAM;IACH,OAAe;IACf,MAAc;IACd,UAAkB;IAClB,YAAoB;IAC5B,OAAe,SAAyB;IAExC,YAAY,MAAiB,CAAE;QAC7B,MAAM,SAAS,QAAQ,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;QAC/D,MAAM,UAAU,QAAQ,WAAW,QAAQ,GAAG,CAAC,eAAe;QAE9D,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,QAAQ,GAAG,CAAC,YAAY,IAAI;QAC1D,IAAI,CAAC,SAAS,GAAG,QAAQ,aAAa,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;QAChF,IAAI,CAAC,WAAW,GAAG,QAAQ,eAAe,WAAW,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAEvF,IAAI,CAAC,MAAM,GAAG,IAAI,wKAAA,CAAA,UAAM,CAAC;YACvB;YACA;QACF;IACF;IAEA,OAAO,YAAY,MAAiB,EAAkB;QACpD,IAAI,CAAC,eAAe,QAAQ,EAAE;YAC5B,eAAe,QAAQ,GAAG,IAAI,eAAe;QAC/C;QACA,OAAO,eAAe,QAAQ;IAChC;IAEA,MAAM,YAAY,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAiC;QACpG,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,SAAS,UAAU;YAE5D,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa,IAAI,CAAC,WAAW;gBAC7B,YAAY,IAAI,CAAC,SAAS;YAC5B;YAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC7C,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2BAA2B;YAC3B,OAAO;gBACL,WAAW,EAAE;gBACb,SAAS,EAAE;gBACX,SAAS,EAAE;gBACX,SAAS,EAAE;YACb;QACF;IACF;IAEQ,qBAAqB,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAU;QACxF,OAAO,CAAC;aACC,EAAE,SAAS,YAAY,EAAE,SAAS;;;MAGzC,EAAE,SAAS;AACjB,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDV,CAAC;IACC;IAEQ,gBAAgB,QAAgB,EAAwB;QAC9D,IAAI;YACF,8DAA8D;YAC9D,MAAM,kBAAkB,SACrB,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,WAAW,IACnB,IAAI;YAEP,MAAM,SAAS,KAAK,KAAK,CAAC;YAE1B,sCAAsC;YACtC,OAAO;gBACL,WAAW,MAAM,OAAO,CAAC,OAAO,SAAS,IAAI,OAAO,SAAS,GAAG,EAAE;gBAClE,SAAS,MAAM,OAAO,CAAC,OAAO,OAAO,IAAI,OAAO,OAAO,GAAG,EAAE;gBAC5D,SAAS,MAAM,OAAO,CAAC,OAAO,OAAO,IAAI,OAAO,OAAO,GAAG,EAAE;gBAC5D,SAAS,MAAM,OAAO,CAAC,OAAO,OAAO,IAAI,OAAO,OAAO,GAAG,EAAE;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,QAAQ,KAAK,CAAC,iBAAiB;YAE/B,qCAAqC;YACrC,OAAO;gBACL,WAAW,EAAE;gBACb,SAAS,EAAE;gBACX,SAAS,EAAE;gBACX,SAAS,EAAE;YACb;QACF;IACF;IAEA,wDAAwD;IACxD,OAAe,QAAQ,IAAI,MAAoC;IAE/D,MAAM,qBAAqB,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAiC;QAC7G,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAEhD,IAAI,eAAe,KAAK,CAAC,GAAG,CAAC,WAAW;YACtC,OAAO,eAAe,KAAK,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,UAAU;QACzD,eAAe,KAAK,CAAC,GAAG,CAAC,UAAU;QAEnC,OAAO;IACT;IAEQ,iBAAiB,OAAe,EAAE,QAAgB,EAAU;QAClE,mCAAmC;QACnC,IAAI,OAAO;QACX,MAAM,MAAM,UAAU;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,IAAI,UAAU,CAAC;YAC5B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;YAC9B,OAAO,OAAO,MAAM,4BAA4B;QAClD;QACA,OAAO,KAAK,QAAQ;IACtB;IAEA,0BAA0B;IAC1B,OAAO,aAAmB;QACxB,eAAe,KAAK,CAAC,KAAK;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/lib/codeAnalyzer.ts"], "sourcesContent": ["import { FileNode, DependencyEdge, FunctionInfo, FunctionCall } from '@/types';\nimport { AICodeAnalyzer, AIConfig } from './aiCodeAnalyzer';\n\nexport class CodeAnalyzer {\n  private static readonly SUPPORTED_EXTENSIONS = {\n    '.js': 'javascript',\n    '.jsx': 'javascript',\n    '.ts': 'typescript',\n    '.tsx': 'typescript',\n    '.py': 'python',\n    '.java': 'java',\n    '.cpp': 'cpp',\n    '.c': 'c',\n    '.cs': 'csharp',\n    '.php': 'php',\n    '.rb': 'ruby',\n    '.go': 'go',\n    '.rs': 'rust',\n    '.swift': 'swift',\n    '.kt': 'kotlin',\n    '.scala': 'scala',\n    '.html': 'html',\n    '.css': 'css',\n    '.scss': 'scss',\n    '.sass': 'sass',\n    '.less': 'less',\n    '.json': 'json',\n    '.xml': 'xml',\n    '.yaml': 'yaml',\n    '.yml': 'yaml',\n    '.md': 'markdown',\n    '.sql': 'sql',\n  };\n\n  static getLanguageFromExtension(filename: string): string | undefined {\n    const ext = filename.substring(filename.lastIndexOf('.'));\n    return this.SUPPORTED_EXTENSIONS[ext as keyof typeof this.SUPPORTED_EXTENSIONS];\n  }\n\n  static extractDependencies(content: string, language: string): string[] {\n    const dependencies: string[] = [];\n\n    switch (language) {\n      case 'javascript':\n      case 'typescript':\n        dependencies.push(...this.extractJavaScriptDependencies(content));\n        break;\n      case 'python':\n        dependencies.push(...this.extractPythonDependencies(content));\n        break;\n      case 'java':\n        dependencies.push(...this.extractJavaDependencies(content));\n        break;\n      case 'cpp':\n      case 'c':\n        dependencies.push(...this.extractCDependencies(content));\n        break;\n      case 'csharp':\n        dependencies.push(...this.extractCSharpDependencies(content));\n        break;\n      case 'go':\n        dependencies.push(...this.extractGoDependencies(content));\n        break;\n      case 'rust':\n        dependencies.push(...this.extractRustDependencies(content));\n        break;\n      default:\n        break;\n    }\n\n    return [...new Set(dependencies)]; // Remove duplicates\n  }\n\n  static async extractFunctions(content: string, language: string, filename: string = 'unknown'): Promise<FunctionInfo[]> {\n    // Check if AI analysis is enabled\n    const enableAI = process.env.ENABLE_AI_ANALYSIS === 'true' && process.env.OPENAI_API_KEY;\n\n    if (enableAI) {\n      try {\n        // Try to get config from localStorage first, then fall back to environment variables\n        let aiConfig: AIConfig;\n\n        if (typeof window !== 'undefined') {\n          const savedConfig = localStorage.getItem('aiProviderConfig');\n          if (savedConfig) {\n            const parsed = JSON.parse(savedConfig);\n            aiConfig = {\n              apiKey: parsed.apiKey,\n              baseURL: parsed.baseURL,\n              model: parsed.model,\n              maxTokens: parsed.maxTokens,\n              temperature: parsed.temperature,\n            };\n          } else {\n            // Fallback to environment variables\n            aiConfig = {\n              apiKey: process.env.OPENAI_API_KEY || '',\n              baseURL: process.env.OPENAI_BASE_URL,\n              model: process.env.OPENAI_MODEL,\n              maxTokens: process.env.OPENAI_MAX_TOKENS ? parseInt(process.env.OPENAI_MAX_TOKENS) : undefined,\n              temperature: process.env.OPENAI_TEMPERATURE ? parseFloat(process.env.OPENAI_TEMPERATURE) : undefined,\n            };\n          }\n        } else {\n          // Server-side: use environment variables\n          aiConfig = {\n            apiKey: process.env.OPENAI_API_KEY || '',\n            baseURL: process.env.OPENAI_BASE_URL,\n            model: process.env.OPENAI_MODEL,\n            maxTokens: process.env.OPENAI_MAX_TOKENS ? parseInt(process.env.OPENAI_MAX_TOKENS) : undefined,\n            temperature: process.env.OPENAI_TEMPERATURE ? parseFloat(process.env.OPENAI_TEMPERATURE) : undefined,\n          };\n        }\n\n        const aiAnalyzer = AICodeAnalyzer.getInstance(aiConfig);\n        const result = await aiAnalyzer.analyzeCodeWithCache(content, language, filename);\n        return result.functions;\n      } catch (error) {\n        console.warn('AI analysis failed, falling back to regex analysis:', error);\n      }\n    }\n\n    // Fallback to regex-based analysis\n    return this.extractFunctionsRegex(content, language);\n  }\n\n  private static extractFunctionsRegex(content: string, language: string): FunctionInfo[] {\n    // Simple regex-based fallback for basic function detection\n    const functions: FunctionInfo[] = [];\n    const lines = content.split('\\n');\n\n    switch (language) {\n      case 'javascript':\n      case 'typescript':\n        functions.push(...this.extractJavaScriptFunctionsRegex(lines));\n        break;\n      case 'python':\n        functions.push(...this.extractPythonFunctionsRegex(lines));\n        break;\n      default:\n        // For other languages, provide basic function detection\n        functions.push(...this.extractGenericFunctionsRegex(lines, language));\n        break;\n    }\n\n    return functions;\n  }\n\n  private static extractJavaScriptFunctionsRegex(lines: string[]): FunctionInfo[] {\n    const functions: FunctionInfo[] = [];\n\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n\n      // Function declarations: function name() {}\n      const funcMatch = line.match(/^(?:export\\s+)?(?:async\\s+)?function\\s+(\\w+)\\s*\\(/);\n      if (funcMatch) {\n        functions.push({\n          name: funcMatch[1],\n          startLine: i + 1,\n          endLine: i + 1, // Simple approximation\n          parameters: [],\n          isExported: line.includes('export'),\n          isAsync: line.includes('async'),\n          calls: []\n        });\n      }\n\n      // Arrow functions: const name = () => {}\n      const arrowMatch = line.match(/^(?:export\\s+)?const\\s+(\\w+)\\s*=\\s*(?:async\\s+)?\\(/);\n      if (arrowMatch) {\n        functions.push({\n          name: arrowMatch[1],\n          startLine: i + 1,\n          endLine: i + 1,\n          parameters: [],\n          isExported: line.includes('export'),\n          isAsync: line.includes('async'),\n          calls: []\n        });\n      }\n    }\n\n    return functions;\n  }\n\n  private static extractPythonFunctionsRegex(lines: string[]): FunctionInfo[] {\n    const functions: FunctionInfo[] = [];\n\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n\n      // Python function definitions: def name():\n      const funcMatch = line.match(/^(?:async\\s+)?def\\s+(\\w+)\\s*\\(/);\n      if (funcMatch) {\n        functions.push({\n          name: funcMatch[1],\n          startLine: i + 1,\n          endLine: i + 1,\n          parameters: [],\n          isAsync: line.includes('async'),\n          calls: []\n        });\n      }\n    }\n\n    return functions;\n  }\n\n  private static extractGenericFunctionsRegex(lines: string[], language: string): FunctionInfo[] {\n    // Very basic function detection for other languages\n    const functions: FunctionInfo[] = [];\n\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n\n      // Generic pattern for function-like constructs\n      const patterns = [\n        /function\\s+(\\w+)/,  // function keyword\n        /def\\s+(\\w+)/,       // Python def\n        /fn\\s+(\\w+)/,        // Rust fn\n        /func\\s+(\\w+)/,      // Go func\n        /(\\w+)\\s*\\(/         // Generic name(\n      ];\n\n      for (const pattern of patterns) {\n        const match = line.match(pattern);\n        if (match) {\n          functions.push({\n            name: match[1],\n            startLine: i + 1,\n            endLine: i + 1,\n            parameters: [],\n            calls: []\n          });\n          break;\n        }\n      }\n    }\n\n    return functions;\n  }\n\n  private static extractJavaScriptDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // ES6 imports\n    const importRegex = /import\\s+(?:.*\\s+from\\s+)?['\"`]([^'\"`]+)['\"`]/g;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    // CommonJS requires\n    const requireRegex = /require\\s*\\(\\s*['\"`]([^'\"`]+)['\"`]\\s*\\)/g;\n    while ((match = requireRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    // Dynamic imports\n    const dynamicImportRegex = /import\\s*\\(\\s*['\"`]([^'\"`]+)['\"`]\\s*\\)/g;\n    while ((match = dynamicImportRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    return dependencies;\n  }\n\n  private static extractPythonDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // import statements\n    const importRegex = /^import\\s+([^\\s#]+)/gm;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      dependencies.push(match[1].split('.')[0]);\n    }\n\n    // from ... import statements\n    const fromImportRegex = /^from\\s+([^\\s#]+)\\s+import/gm;\n    while ((match = fromImportRegex.exec(content)) !== null) {\n      dependencies.push(match[1].split('.')[0]);\n    }\n\n    return dependencies;\n  }\n\n  private static extractJavaDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // import statements\n    const importRegex = /^import\\s+(?:static\\s+)?([^;]+);/gm;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const packageName = match[1].trim();\n      dependencies.push(packageName);\n    }\n\n    return dependencies;\n  }\n\n  private static extractCDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // #include statements\n    const includeRegex = /#include\\s*[<\"]([^>\"]+)[>\"]/g;\n    let match;\n    while ((match = includeRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    return dependencies;\n  }\n\n  private static extractCSharpDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // using statements\n    const usingRegex = /^using\\s+([^;]+);/gm;\n    let match;\n    while ((match = usingRegex.exec(content)) !== null) {\n      dependencies.push(match[1].trim());\n    }\n\n    return dependencies;\n  }\n\n  private static extractGoDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // import statements\n    const importRegex = /import\\s+(?:\\(\\s*([^)]+)\\s*\\)|\"([^\"]+)\")/g;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      if (match[1]) {\n        // Multi-line import\n        const imports = match[1].split('\\n').map(line => {\n          const trimmed = line.trim();\n          const quoted = trimmed.match(/\"([^\"]+)\"/);\n          return quoted ? quoted[1] : null;\n        }).filter(Boolean);\n        dependencies.push(...imports as string[]);\n      } else if (match[2]) {\n        // Single import\n        dependencies.push(match[2]);\n      }\n    }\n\n    return dependencies;\n  }\n\n  private static extractRustDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // use statements\n    const useRegex = /^use\\s+([^;]+);/gm;\n    let match;\n    while ((match = useRegex.exec(content)) !== null) {\n      const usePath = match[1].trim();\n      const rootModule = usePath.split('::')[0];\n      dependencies.push(rootModule);\n    }\n\n    // extern crate statements\n    const externRegex = /^extern\\s+crate\\s+([^;]+);/gm;\n    while ((match = externRegex.exec(content)) !== null) {\n      dependencies.push(match[1].trim());\n    }\n\n    return dependencies;\n  }\n\n  static createDependencyEdges(nodes: FileNode[]): DependencyEdge[] {\n    const edges: DependencyEdge[] = [];\n    const nodeMap = new Map(nodes.map(node => [node.path, node]));\n    const functionMap = new Map<string, { node: FileNode, function: FunctionInfo }>();\n\n    // Build function map for quick lookup\n    nodes.forEach(node => {\n      if (node.functions) {\n        node.functions.forEach(func => {\n          functionMap.set(`${node.path}:${func.name}`, { node, function: func });\n        });\n      }\n    });\n\n    nodes.forEach(node => {\n      // Add import/require edges\n      if (node.dependencies) {\n        node.dependencies.forEach(dep => {\n          const targetNode = this.findDependencyTarget(dep, nodeMap, node.path);\n          if (targetNode) {\n            edges.push({\n              id: `import-${node.id}-${targetNode.id}`,\n              source: node.id,\n              target: targetNode.id,\n              type: 'import',\n              label: dep,\n            });\n          }\n        });\n      }\n\n      // Add function call edges\n      if (node.functions) {\n        node.functions.forEach(func => {\n          if (func.calls) {\n            func.calls.forEach(call => {\n              if (!call.isExternal) {\n                // Try to find the target function in the same file first\n                const sameFileTarget = functionMap.get(`${node.path}:${call.functionName}`);\n                if (sameFileTarget) {\n                  edges.push({\n                    id: `call-${node.id}-${func.name}-${call.functionName}-${call.line}`,\n                    source: node.id,\n                    target: node.id, // Same file\n                    type: 'function_call',\n                    label: `${func.name} → ${call.functionName}`,\n                    sourceFunction: func.name,\n                    targetFunction: call.functionName,\n                    line: call.line,\n                  });\n                } else if (call.targetFile) {\n                  // Try to find in other files\n                  const targetNode = nodeMap.get(call.targetFile);\n                  if (targetNode) {\n                    edges.push({\n                      id: `call-${node.id}-${targetNode.id}-${func.name}-${call.functionName}-${call.line}`,\n                      source: node.id,\n                      target: targetNode.id,\n                      type: 'function_call',\n                      label: `${func.name} → ${call.functionName}`,\n                      sourceFunction: func.name,\n                      targetFunction: call.functionName,\n                      line: call.line,\n                    });\n                  }\n                }\n              }\n            });\n          }\n        });\n      }\n    });\n\n    return edges;\n  }\n\n  private static findDependencyTarget(\n    dependency: string,\n    nodeMap: Map<string, FileNode>,\n    sourcePath: string\n  ): FileNode | null {\n    // Handle relative imports\n    if (dependency.startsWith('./') || dependency.startsWith('../')) {\n      const resolvedPath = this.resolvePath(sourcePath, dependency);\n      return nodeMap.get(resolvedPath) || null;\n    }\n\n    // Handle absolute imports within the project\n    for (const [path, node] of nodeMap) {\n      if (path.includes(dependency) || node.name.includes(dependency)) {\n        return node;\n      }\n    }\n\n    return null;\n  }\n\n  private static resolvePath(basePath: string, relativePath: string): string {\n    const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));\n    const parts = baseDir.split('/').concat(relativePath.split('/'));\n    const resolved: string[] = [];\n\n    parts.forEach(part => {\n      if (part === '..') {\n        resolved.pop();\n      } else if (part !== '.' && part !== '') {\n        resolved.push(part);\n      }\n    });\n\n    return resolved.join('/');\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,OAAwB,uBAAuB;QAC7C,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,EAAE;IAEF,OAAO,yBAAyB,QAAgB,EAAsB;QACpE,MAAM,MAAM,SAAS,SAAS,CAAC,SAAS,WAAW,CAAC;QACpD,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAA8C;IACjF;IAEA,OAAO,oBAAoB,OAAe,EAAE,QAAgB,EAAY;QACtE,MAAM,eAAyB,EAAE;QAEjC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,6BAA6B,CAAC;gBACxD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC;gBACpD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAC;gBAClD;YACF,KAAK;YACL,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC;gBAC/C;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC;gBACpD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC;gBAChD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAC;gBAClD;YACF;gBACE;QACJ;QAEA,OAAO;eAAI,IAAI,IAAI;SAAc,EAAE,oBAAoB;IACzD;IAEA,aAAa,iBAAiB,OAAe,EAAE,QAAgB,EAAE,WAAmB,SAAS,EAA2B;QACtH,kCAAkC;QAClC,MAAM,WAAW,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAAU,QAAQ,GAAG,CAAC,cAAc;QAExF,IAAI,UAAU;YACZ,IAAI;gBACF,qFAAqF;gBACrF,IAAI;gBAEJ;;qBAqBO;oBACL,yCAAyC;oBACzC,WAAW;wBACT,QAAQ,QAAQ,GAAG,CAAC,cAAc,IAAI;wBACtC,SAAS,QAAQ,GAAG,CAAC,eAAe;wBACpC,OAAO,QAAQ,GAAG,CAAC,YAAY;wBAC/B,WAAW,QAAQ,GAAG,CAAC,iBAAiB,GAAG,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;wBACrF,aAAa,QAAQ,GAAG,CAAC,kBAAkB,GAAG,WAAW,QAAQ,GAAG,CAAC,kBAAkB,IAAI;oBAC7F;gBACF;gBAEA,MAAM,aAAa,8HAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;gBAC9C,MAAM,SAAS,MAAM,WAAW,oBAAoB,CAAC,SAAS,UAAU;gBACxE,OAAO,OAAO,SAAS;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,uDAAuD;YACtE;QACF;QAEA,mCAAmC;QACnC,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS;IAC7C;IAEA,OAAe,sBAAsB,OAAe,EAAE,QAAgB,EAAkB;QACtF,2DAA2D;QAC3D,MAAM,YAA4B,EAAE;QACpC,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,UAAU,IAAI,IAAI,IAAI,CAAC,+BAA+B,CAAC;gBACvD;YACF,KAAK;gBACH,UAAU,IAAI,IAAI,IAAI,CAAC,2BAA2B,CAAC;gBACnD;YACF;gBACE,wDAAwD;gBACxD,UAAU,IAAI,IAAI,IAAI,CAAC,4BAA4B,CAAC,OAAO;gBAC3D;QACJ;QAEA,OAAO;IACT;IAEA,OAAe,gCAAgC,KAAe,EAAkB;QAC9E,MAAM,YAA4B,EAAE;QAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YAE1B,4CAA4C;YAC5C,MAAM,YAAY,KAAK,KAAK,CAAC;YAC7B,IAAI,WAAW;gBACb,UAAU,IAAI,CAAC;oBACb,MAAM,SAAS,CAAC,EAAE;oBAClB,WAAW,IAAI;oBACf,SAAS,IAAI;oBACb,YAAY,EAAE;oBACd,YAAY,KAAK,QAAQ,CAAC;oBAC1B,SAAS,KAAK,QAAQ,CAAC;oBACvB,OAAO,EAAE;gBACX;YACF;YAEA,yCAAyC;YACzC,MAAM,aAAa,KAAK,KAAK,CAAC;YAC9B,IAAI,YAAY;gBACd,UAAU,IAAI,CAAC;oBACb,MAAM,UAAU,CAAC,EAAE;oBACnB,WAAW,IAAI;oBACf,SAAS,IAAI;oBACb,YAAY,EAAE;oBACd,YAAY,KAAK,QAAQ,CAAC;oBAC1B,SAAS,KAAK,QAAQ,CAAC;oBACvB,OAAO,EAAE;gBACX;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,4BAA4B,KAAe,EAAkB;QAC1E,MAAM,YAA4B,EAAE;QAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YAE1B,2CAA2C;YAC3C,MAAM,YAAY,KAAK,KAAK,CAAC;YAC7B,IAAI,WAAW;gBACb,UAAU,IAAI,CAAC;oBACb,MAAM,SAAS,CAAC,EAAE;oBAClB,WAAW,IAAI;oBACf,SAAS,IAAI;oBACb,YAAY,EAAE;oBACd,SAAS,KAAK,QAAQ,CAAC;oBACvB,OAAO,EAAE;gBACX;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,6BAA6B,KAAe,EAAE,QAAgB,EAAkB;QAC7F,oDAAoD;QACpD,MAAM,YAA4B,EAAE;QAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YAE1B,+CAA+C;YAC/C,MAAM,WAAW;gBACf;gBACA;gBACA;gBACA;gBACA,aAAqB,gBAAgB;aACtC;YAED,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC;wBACb,MAAM,KAAK,CAAC,EAAE;wBACd,WAAW,IAAI;wBACf,SAAS,IAAI;wBACb,YAAY,EAAE;wBACd,OAAO,EAAE;oBACX;oBACA;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,8BAA8B,OAAe,EAAY;QACtE,MAAM,eAAyB,EAAE;QAEjC,cAAc;QACd,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,oBAAoB;QACpB,MAAM,eAAe;QACrB,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,QAAQ,MAAM,KAAM;YACpD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,kBAAkB;QAClB,MAAM,qBAAqB;QAC3B,MAAO,CAAC,QAAQ,mBAAmB,IAAI,CAAC,QAAQ,MAAM,KAAM;YAC1D,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,OAAO;IACT;IAEA,OAAe,0BAA0B,OAAe,EAAY;QAClE,MAAM,eAAyB,EAAE;QAEjC,oBAAoB;QACpB,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB;QACxB,MAAO,CAAC,QAAQ,gBAAgB,IAAI,CAAC,QAAQ,MAAM,KAAM;YACvD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C;QAEA,OAAO;IACT;IAEA,OAAe,wBAAwB,OAAe,EAAY;QAChE,MAAM,eAAyB,EAAE;QAEjC,oBAAoB;QACpB,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,MAAM,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI;YACjC,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;IACT;IAEA,OAAe,qBAAqB,OAAe,EAAY;QAC7D,MAAM,eAAyB,EAAE;QAEjC,sBAAsB;QACtB,MAAM,eAAe;QACrB,IAAI;QACJ,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,QAAQ,MAAM,KAAM;YACpD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,OAAO;IACT;IAEA,OAAe,0BAA0B,OAAe,EAAY;QAClE,MAAM,eAAyB,EAAE;QAEjC,mBAAmB;QACnB,MAAM,aAAa;QACnB,IAAI;QACJ,MAAO,CAAC,QAAQ,WAAW,IAAI,CAAC,QAAQ,MAAM,KAAM;YAClD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QACjC;QAEA,OAAO;IACT;IAEA,OAAe,sBAAsB,OAAe,EAAY;QAC9D,MAAM,eAAyB,EAAE;QAEjC,oBAAoB;QACpB,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,IAAI,KAAK,CAAC,EAAE,EAAE;gBACZ,oBAAoB;gBACpB,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;oBACvC,MAAM,UAAU,KAAK,IAAI;oBACzB,MAAM,SAAS,QAAQ,KAAK,CAAC;oBAC7B,OAAO,SAAS,MAAM,CAAC,EAAE,GAAG;gBAC9B,GAAG,MAAM,CAAC;gBACV,aAAa,IAAI,IAAI;YACvB,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE;gBACnB,gBAAgB;gBAChB,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;YAC5B;QACF;QAEA,OAAO;IACT;IAEA,OAAe,wBAAwB,OAAe,EAAY;QAChE,MAAM,eAAyB,EAAE;QAEjC,iBAAiB;QACjB,MAAM,WAAW;QACjB,IAAI;QACJ,MAAO,CAAC,QAAQ,SAAS,IAAI,CAAC,QAAQ,MAAM,KAAM;YAChD,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,IAAI;YAC7B,MAAM,aAAa,QAAQ,KAAK,CAAC,KAAK,CAAC,EAAE;YACzC,aAAa,IAAI,CAAC;QACpB;QAEA,0BAA0B;QAC1B,MAAM,cAAc;QACpB,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QACjC;QAEA,OAAO;IACT;IAEA,OAAO,sBAAsB,KAAiB,EAAoB;QAChE,MAAM,QAA0B,EAAE;QAClC,MAAM,UAAU,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ;gBAAC,KAAK,IAAI;gBAAE;aAAK;QAC3D,MAAM,cAAc,IAAI;QAExB,sCAAsC;QACtC,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,SAAS,EAAE;gBAClB,KAAK,SAAS,CAAC,OAAO,CAAC,CAAA;oBACrB,YAAY,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE;wBAAE;wBAAM,UAAU;oBAAK;gBACtE;YACF;QACF;QAEA,MAAM,OAAO,CAAC,CAAA;YACZ,2BAA2B;YAC3B,IAAI,KAAK,YAAY,EAAE;gBACrB,KAAK,YAAY,CAAC,OAAO,CAAC,CAAA;oBACxB,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC,KAAK,SAAS,KAAK,IAAI;oBACpE,IAAI,YAAY;wBACd,MAAM,IAAI,CAAC;4BACT,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE;4BACxC,QAAQ,KAAK,EAAE;4BACf,QAAQ,WAAW,EAAE;4BACrB,MAAM;4BACN,OAAO;wBACT;oBACF;gBACF;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,SAAS,EAAE;gBAClB,KAAK,SAAS,CAAC,OAAO,CAAC,CAAA;oBACrB,IAAI,KAAK,KAAK,EAAE;wBACd,KAAK,KAAK,CAAC,OAAO,CAAC,CAAA;4BACjB,IAAI,CAAC,KAAK,UAAU,EAAE;gCACpB,yDAAyD;gCACzD,MAAM,iBAAiB,YAAY,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE;gCAC1E,IAAI,gBAAgB;oCAClB,MAAM,IAAI,CAAC;wCACT,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;wCACpE,QAAQ,KAAK,EAAE;wCACf,QAAQ,KAAK,EAAE;wCACf,MAAM;wCACN,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,YAAY,EAAE;wCAC5C,gBAAgB,KAAK,IAAI;wCACzB,gBAAgB,KAAK,YAAY;wCACjC,MAAM,KAAK,IAAI;oCACjB;gCACF,OAAO,IAAI,KAAK,UAAU,EAAE;oCAC1B,6BAA6B;oCAC7B,MAAM,aAAa,QAAQ,GAAG,CAAC,KAAK,UAAU;oCAC9C,IAAI,YAAY;wCACd,MAAM,IAAI,CAAC;4CACT,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;4CACrF,QAAQ,KAAK,EAAE;4CACf,QAAQ,WAAW,EAAE;4CACrB,MAAM;4CACN,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,YAAY,EAAE;4CAC5C,gBAAgB,KAAK,IAAI;4CACzB,gBAAgB,KAAK,YAAY;4CACjC,MAAM,KAAK,IAAI;wCACjB;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,qBACb,UAAkB,EAClB,OAA8B,EAC9B,UAAkB,EACD;QACjB,0BAA0B;QAC1B,IAAI,WAAW,UAAU,CAAC,SAAS,WAAW,UAAU,CAAC,QAAQ;YAC/D,MAAM,eAAe,IAAI,CAAC,WAAW,CAAC,YAAY;YAClD,OAAO,QAAQ,GAAG,CAAC,iBAAiB;QACtC;QAEA,6CAA6C;QAC7C,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,QAAS;YAClC,IAAI,KAAK,QAAQ,CAAC,eAAe,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa;gBAC/D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,OAAe,YAAY,QAAgB,EAAE,YAAoB,EAAU;QACzE,MAAM,UAAU,SAAS,SAAS,CAAC,GAAG,SAAS,WAAW,CAAC;QAC3D,MAAM,QAAQ,QAAQ,KAAK,CAAC,KAAK,MAAM,CAAC,aAAa,KAAK,CAAC;QAC3D,MAAM,WAAqB,EAAE;QAE7B,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,SAAS,MAAM;gBACjB,SAAS,GAAG;YACd,OAAO,IAAI,SAAS,OAAO,SAAS,IAAI;gBACtC,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;AACF", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { GitHubService } from '@/lib/github';\nimport { CodeAnalyzer } from '@/lib/codeAnalyzer';\nimport { AnalysisResult, FileNode, CodeGraph } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url, token } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'Repository URL is required' },\n        { status: 400 }\n      );\n    }\n\n    // Environment variable'dan token'ı al, eğer frontend'den gönderilmemişse\n    const githubToken = token || process.env.GITHUB_TOKEN;\n    const githubService = new GitHubService(githubToken);\n    const repoInfo = githubService.parseRepoUrl(url);\n\n    if (!repoInfo) {\n      return NextResponse.json(\n        { error: 'Invalid GitHub URL format' },\n        { status: 400 }\n      );\n    }\n\n    const { owner, repo } = repoInfo;\n\n    try {\n      // Repository bilgilerini al\n      const repository = await githubService.getRepository(owner, repo);\n      const languages = await githubService.getRepositoryLanguages(owner, repo);\n\n      // Repository içeriğini analiz et\n      const nodes = await analyzeRepositoryContents(githubService, owner, repo);\n      const edges = CodeAnalyzer.createDependencyEdges(nodes);\n\n      const graph: CodeGraph = { nodes, edges };\n\n      const result: AnalysisResult = {\n        repo: repository,\n        graph,\n        languages,\n        stats: {\n          totalFiles: nodes.filter(n => n.type === 'file').length,\n          totalDirectories: nodes.filter(n => n.type === 'directory').length,\n          totalDependencies: edges.filter(e => e.type === 'import' || e.type === 'require' || e.type === 'include').length,\n          totalFunctions: nodes.reduce((sum, n) => sum + (n.functions?.length || 0), 0),\n          totalFunctionCalls: edges.filter(e => e.type === 'function_call').length,\n        },\n      };\n\n      return NextResponse.json(result);\n    } catch (error) {\n      console.error('GitHub API Error:', error);\n      return NextResponse.json(\n        { error: 'Failed to fetch repository data. Please check if the repository exists and is public.' },\n        { status: 404 }\n      );\n    }\n  } catch (error) {\n    console.error('Analysis Error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function analyzeRepositoryContents(\n  githubService: GitHubService,\n  owner: string,\n  repo: string,\n  path: string = '',\n  depth: number = 0\n): Promise<FileNode[]> {\n  const nodes: FileNode[] = [];\n  const maxDepth = 3; // Maksimum derinlik sınırı\n  const maxFiles = 100; // Maksimum dosya sayısı sınırı\n\n  if (depth >= maxDepth) {\n    return nodes;\n  }\n\n  try {\n    const contents = await githubService.getRepositoryContents(owner, repo, path);\n    \n    // İçerikleri sırala: önce klasörler, sonra dosyalar\n    const sortedContents = contents.sort((a, b) => {\n      if (a.type === 'dir' && b.type === 'file') return -1;\n      if (a.type === 'file' && b.type === 'dir') return 1;\n      return a.name.localeCompare(b.name);\n    });\n\n    let fileCount = 0;\n\n    for (const item of sortedContents) {\n      if (fileCount >= maxFiles) break;\n\n      const nodeId = `${path}/${item.name}`.replace(/^\\//, '') || item.name;\n      const node: FileNode = {\n        id: nodeId,\n        name: item.name,\n        path: item.path,\n        type: item.type === 'dir' ? 'directory' : 'file',\n        size: item.size,\n      };\n\n      if (item.type === 'file') {\n        fileCount++;\n        const language = CodeAnalyzer.getLanguageFromExtension(item.name);\n        if (language) {\n          node.language = language;\n          \n          // Sadece küçük dosyaları analiz et (50KB'den küçük)\n          if (item.size && item.size < 50000) {\n            try {\n              const content = await githubService.getFileContent(owner, repo, item.path);\n              node.content = content;\n              node.dependencies = CodeAnalyzer.extractDependencies(content, language);\n\n              // Extract functions using AI or fallback to regex\n              try {\n                node.functions = await CodeAnalyzer.extractFunctions(content, language, item.name);\n              } catch (functionError) {\n                console.warn(`Could not extract functions for ${item.path}:`, functionError);\n                node.functions = [];\n              }\n            } catch (contentError) {\n              console.warn(`Could not fetch content for ${item.path}:`, contentError);\n            }\n          }\n        }\n      } else if (item.type === 'dir') {\n        // Belirli klasörleri atla\n        const skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', 'coverage', '__pycache__'];\n        if (!skipDirs.includes(item.name)) {\n          const children = await analyzeRepositoryContents(\n            githubService,\n            owner,\n            repo,\n            item.path,\n            depth + 1\n          );\n          node.children = children;\n          nodes.push(...children);\n        }\n      }\n\n      nodes.push(node);\n    }\n  } catch (error) {\n    console.warn(`Could not analyze path ${path}:`, error);\n  }\n\n  return nodes;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,yEAAyE;QACzE,MAAM,cAAc,SAAS,QAAQ,GAAG,CAAC,YAAY;QACrD,MAAM,gBAAgB,IAAI,sHAAA,CAAA,gBAAa,CAAC;QACxC,MAAM,WAAW,cAAc,YAAY,CAAC;QAE5C,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;QAExB,IAAI;YACF,4BAA4B;YAC5B,MAAM,aAAa,MAAM,cAAc,aAAa,CAAC,OAAO;YAC5D,MAAM,YAAY,MAAM,cAAc,sBAAsB,CAAC,OAAO;YAEpE,iCAAiC;YACjC,MAAM,QAAQ,MAAM,0BAA0B,eAAe,OAAO;YACpE,MAAM,QAAQ,4HAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC;YAEjD,MAAM,QAAmB;gBAAE;gBAAO;YAAM;YAExC,MAAM,SAAyB;gBAC7B,MAAM;gBACN;gBACA;gBACA,OAAO;oBACL,YAAY,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;oBACvD,kBAAkB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;oBAClE,mBAAmB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,WAAW,MAAM;oBAChH,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,GAAG;oBAC3E,oBAAoB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,iBAAiB,MAAM;gBAC1E;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwF,GACjG;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,0BACb,aAA4B,EAC5B,KAAa,EACb,IAAY,EACZ,OAAe,EAAE,EACjB,QAAgB,CAAC;IAEjB,MAAM,QAAoB,EAAE;IAC5B,MAAM,WAAW,GAAG,2BAA2B;IAC/C,MAAM,WAAW,KAAK,+BAA+B;IAErD,IAAI,SAAS,UAAU;QACrB,OAAO;IACT;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,cAAc,qBAAqB,CAAC,OAAO,MAAM;QAExE,oDAAoD;QACpD,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAC,GAAG;YACvC,IAAI,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,KAAK,QAAQ,OAAO,CAAC;YACnD,IAAI,EAAE,IAAI,KAAK,UAAU,EAAE,IAAI,KAAK,OAAO,OAAO;YAClD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC;QAEA,IAAI,YAAY;QAEhB,KAAK,MAAM,QAAQ,eAAgB;YACjC,IAAI,aAAa,UAAU;YAE3B,MAAM,SAAS,GAAG,KAAK,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,OAAO,KAAK,IAAI;YACrE,MAAM,OAAiB;gBACrB,IAAI;gBACJ,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI,KAAK,QAAQ,cAAc;gBAC1C,MAAM,KAAK,IAAI;YACjB;YAEA,IAAI,KAAK,IAAI,KAAK,QAAQ;gBACxB;gBACA,MAAM,WAAW,4HAAA,CAAA,eAAY,CAAC,wBAAwB,CAAC,KAAK,IAAI;gBAChE,IAAI,UAAU;oBACZ,KAAK,QAAQ,GAAG;oBAEhB,oDAAoD;oBACpD,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,OAAO;wBAClC,IAAI;4BACF,MAAM,UAAU,MAAM,cAAc,cAAc,CAAC,OAAO,MAAM,KAAK,IAAI;4BACzE,KAAK,OAAO,GAAG;4BACf,KAAK,YAAY,GAAG,4HAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,SAAS;4BAE9D,kDAAkD;4BAClD,IAAI;gCACF,KAAK,SAAS,GAAG,MAAM,4HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,SAAS,UAAU,KAAK,IAAI;4BACnF,EAAE,OAAO,eAAe;gCACtB,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;gCAC9D,KAAK,SAAS,GAAG,EAAE;4BACrB;wBACF,EAAE,OAAO,cAAc;4BACrB,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;wBAC5D;oBACF;gBACF;YACF,OAAO,IAAI,KAAK,IAAI,KAAK,OAAO;gBAC9B,0BAA0B;gBAC1B,MAAM,WAAW;oBAAC;oBAAgB;oBAAQ;oBAAQ;oBAAS;oBAAS;oBAAY;iBAAc;gBAC9F,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK,IAAI,GAAG;oBACjC,MAAM,WAAW,MAAM,0BACrB,eACA,OACA,MACA,KAAK,IAAI,EACT,QAAQ;oBAEV,KAAK,QAAQ,GAAG;oBAChB,MAAM,IAAI,IAAI;gBAChB;YACF;YAEA,MAAM,IAAI,CAAC;QACb;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,EAAE;IAClD;IAEA,OAAO;AACT", "debugId": null}}]}